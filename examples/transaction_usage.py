#!/usr/bin/env python3
"""
事务使用示例

展示正确和错误的事务管理方式
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from app.db.session import get_async_transaction_session


async def correct_transaction_usage():
    """✅ 正确的事务使用方式"""
    logger.info("=== 正确的事务使用方式 ===")
    
    async with get_async_transaction_session() as db:
        try:
            # 模拟一些数据库操作
            logger.info("执行数据库操作...")
            
            # 模拟成功的操作
            success = True
            
            if success:
                await db.commit()
                logger.success("✅ 事务提交成功")
            else:
                # 抛出异常，让会话自动回滚
                raise Exception("操作失败")
                
        except Exception as e:
            logger.error(f"❌ 操作失败: {e}")
            # 不需要手动回滚，会话会自动处理
            raise


async def incorrect_transaction_usage():
    """❌ 错误的事务使用方式（多余的手动回滚）"""
    logger.info("=== 错误的事务使用方式 ===")
    
    async with get_async_transaction_session() as db:
        try:
            # 模拟一些数据库操作
            logger.info("执行数据库操作...")
            
            # 模拟失败的操作
            success = False
            
            if success:
                await db.commit()
                logger.success("✅ 事务提交成功")
            else:
                logger.error("❌ 操作失败")
                await db.rollback()  # ❌ 多余的手动回滚
                raise Exception("操作失败")
                
        except Exception as e:
            logger.error(f"❌ 异常处理: {e}")
            await db.rollback()  # ❌ 多余的手动回滚
            raise


async def demonstrate_auto_rollback():
    """演示自动回滚机制"""
    logger.info("=== 演示自动回滚机制 ===")
    
    try:
        async with get_async_transaction_session() as db:
            logger.info("开始事务...")
            
            # 模拟一些操作
            logger.info("执行操作 1...")
            logger.info("执行操作 2...")
            
            # 模拟异常
            raise Exception("模拟的异常")
            
            # 这行代码不会执行
            await db.commit()
            
    except Exception as e:
        logger.info(f"捕获异常: {e}")
        logger.info("✅ 事务已自动回滚")


async def demonstrate_successful_commit():
    """演示成功提交"""
    logger.info("=== 演示成功提交 ===")
    
    async with get_async_transaction_session() as db:
        logger.info("开始事务...")
        
        # 模拟一些操作
        logger.info("执行操作 1...")
        logger.info("执行操作 2...")
        logger.info("执行操作 3...")
        
        # 所有操作成功，提交事务
        await db.commit()
        logger.success("✅ 事务提交成功")


async def main():
    """主函数"""
    logger.info("事务使用示例")
    
    try:
        # 1. 正确的使用方式
        await correct_transaction_usage()
        
        # 2. 错误的使用方式（仅作演示）
        try:
            await incorrect_transaction_usage()
        except Exception:
            logger.info("错误示例执行完毕")
        
        # 3. 演示自动回滚
        await demonstrate_auto_rollback()
        
        # 4. 演示成功提交
        await demonstrate_successful_commit()
        
        logger.success("🎉 所有示例执行完毕")
        
    except Exception as e:
        logger.error(f"❌ 示例执行失败: {e}")


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level="INFO"
    )
    
    # 运行示例
    asyncio.run(main())
